{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(bs|de|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "24bba4972b5a9f4ed1a6016b213f21e8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fed0763787482f483bb619a5bc044abe191009210e2174bb61a3aba6a964838f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "988428196971141ef5cf4ec56b8a8c42d685d4f7fda10561fa565c96b5d8394a"}}}, "sortedMiddleware": ["/"], "functions": {}}