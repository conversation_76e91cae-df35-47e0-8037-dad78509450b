{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(bs|de|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "a87cfc646b4755f2f26afc5aeeb77c48", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8aee6a7fa38945cef7857a7a7ef03408934cad725783e651278f1179f77026a1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "41bd38ad38a5ee77f7c3c3fabd5a4ea2140ff7299a574f484aa246a9eac99d56"}}}, "sortedMiddleware": ["/"], "functions": {}}