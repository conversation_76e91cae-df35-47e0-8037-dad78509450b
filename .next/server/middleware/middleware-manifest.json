{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(bs|de|en)/:path*{(\\\\.json)}?", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "760892e60fc19b03d33607a79276daba", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c06cf4854e01a7e3961a0350eb1f1d70655e98eaf434b369b0aeabfd649976a5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b43cf33178ca990f3c3154b4b3aca51988c0c0cf031db3d8263f188616bc296e"}}}, "instrumentation": null, "functions": {}}