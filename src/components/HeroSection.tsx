'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useRef } from 'react';
import gradientGL from 'gradient-gl';

const HeroSection = () => {
  const t = useTranslations('hero');
  const gradientRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (gradientRef.current) {
      // Initialize gradient-gl with the specified seed
      gradientGL('f2.e8b3', gradientRef.current);
    }
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* WebGL Gradient Background */}
      <div ref={gradientRef} className="absolute inset-0 z-0"></div>

      {/* Immersive Background with Geometric Elements */}
      <div className="absolute inset-0 z-10">
        {/* Subtle overlay to ensure text readability */}
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Geometric circles - inspired by the designs */}
        <div className="absolute top-20 right-20 w-96 h-96 border border-white/10 rounded-full"></div>
        <div className="absolute top-40 right-40 w-64 h-64 border border-white/20 rounded-full"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 border border-blue-400/20 rounded-full"></div>

        {/* Floating geometric elements */}
        <div className="absolute top-1/3 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
        <div className="absolute top-1/2 right-1/3 w-3 h-3 bg-white/60 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/3 left-1/3 w-2 h-2 bg-indigo-400 rounded-full animate-pulse delay-2000"></div>

        {/* Connecting lines */}
        <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(59, 130, 246, 0.3)" />
              <stop offset="100%" stopColor="rgba(255, 255, 255, 0.1)" />
            </linearGradient>
          </defs>
          <path d="M 200 300 Q 600 200 800 400" stroke="url(#lineGradient)" strokeWidth="1" fill="none" />
          <path d="M 100 500 Q 400 300 700 600" stroke="url(#lineGradient)" strokeWidth="1" fill="none" />
        </svg>

        {/* Blur effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-indigo-500/20 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-20">
        <div className="py-32 space-y-16">
          {/* Main Title - Bold and Impactful */}
          <div className="space-y-8">
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-black leading-[0.9] tracking-tight">
              <span className="block text-white mb-4">
                {t('title').split(' ')[0]}
              </span>
              <span className="block bg-gradient-to-r from-blue-400 via-cyan-300 to-white bg-clip-text text-transparent">
                {t('title').split(' ').slice(1).join(' ')}
              </span>
            </h1>

            <div className="max-w-3xl">
              <p className="text-xl md:text-2xl text-blue-100/90 leading-relaxed font-light">
                {t('description')}
              </p>
            </div>
          </div>

          {/* Enhanced CTA with modern styling */}
          <div className="flex flex-col sm:flex-row gap-6 pt-8">
            <button className="group relative bg-gradient-to-r from-blue-500 to-cyan-400 text-white px-10 py-4 rounded-full font-semibold text-lg hover:shadow-2xl hover:shadow-blue-500/25 transform hover:scale-105 transition-all duration-300 overflow-hidden">
              <span className="relative z-10">Kontaktirajte nas</span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>

            <button className="group flex items-center space-x-3 text-white/80 hover:text-white font-medium transition-all duration-300">
              <div className="w-12 h-12 border border-white/30 rounded-full flex items-center justify-center group-hover:border-white/60 transition-all duration-300">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Pogledajte video</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
